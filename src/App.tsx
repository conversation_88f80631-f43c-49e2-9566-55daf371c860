import React from 'react'
import './App.css'
import SimpleReactionDemo from './components/SimpleReactionDemo'

function App() {
  return (
    <div className="App">
      <header style={{
        background: 'linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%)',
        color: 'white',
        padding: '2.5rem',
        borderRadius: '16px',
        marginBottom: '2rem',
        boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
        textAlign: 'center'
      }}>
        <h1 style={{ margin: 0, fontSize: '3rem' }}>🎯 MobX 实验室</h1>
        <p style={{ margin: '0.8rem 0 0 0', opacity: 0.95, fontSize: '1.2rem' }}>响应式编程的奇妙世界 🌟</p>
        <div style={{ marginTop: '1rem', fontSize: '0.9rem', opacity: 0.8 }}>
          ⚡ 实时更新 | 🔄 自动同步 | 📈 性能优化
        </div>
      </header>
      
      <div style={{
        background: 'linear-gradient(145deg, #ffffff 0%, #f0f8ff 100%)',
        padding: '2rem',
        borderRadius: '12px',
        border: '2px solid #e3f2fd',
        boxShadow: '0 4px 12px rgba(0,0,0,0.08)'
      }}>
        <h3 style={{
          color: '#2c3e50',
          marginTop: 0,
          fontSize: '1.5rem',
          borderBottom: '2px solid #3498db',
          paddingBottom: '0.5rem',
          marginBottom: '1.5rem'
        }}>📊 MobX 响应式数据演示</h3>
        <SimpleReactionDemo />

        <div style={{
          marginTop: '1.5rem',
          padding: '1rem',
          background: '#e8f5e8',
          borderRadius: '8px',
          border: '1px solid #c3e6c3'
        }}>
          <p style={{ margin: 0, color: '#2d5a2d', fontSize: '0.9rem' }}>
            💡 <strong>提示：</strong> 观察数据变化如何自动触发 UI 更新
          </p>
        </div>
      </div>
      
      <footer style={{
        marginTop: '3rem',
        padding: '1.5rem',
        textAlign: 'center',
        background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px',
        boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
      }}>
        <div style={{ marginBottom: '0.5rem' }}>
          <strong>🎉 MobX 状态管理演示项目</strong>
        </div>
        <small style={{ opacity: 0.9 }}>
          ⚡ 响应式 | 🔧 简单易用 | 🚀 高性能
        </small>
        <div style={{ marginTop: '0.8rem', fontSize: '0.8rem', opacity: 0.7 }}>
          Built with React + MobX + TypeScript
        </div>
      </footer>
    </div>
  )
}

export default App
